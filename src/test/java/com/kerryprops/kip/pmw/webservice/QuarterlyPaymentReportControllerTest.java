package com.kerryprops.kip.pmw.webservice;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.pmw.service.QuarterlyPaymentReportService;
import com.kerryprops.kip.pmw.webservice.resource.QuarterlyPaymentReportResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Unit tests for QuarterlyPaymentReportController.
 */
@WebMvcTest(QuarterlyPaymentReportController.class)
class QuarterlyPaymentReportControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private QuarterlyPaymentReportService quarterlyPaymentReportService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void getQuarterlyPaymentReport_ShouldReturnSuccessResponse_WhenDataExists() throws Exception {
        // Given
        List<QuarterlyPaymentReportResponse> mockData = Arrays.asList(
            new QuarterlyPaymentReportResponse("2024Q1", "premium", 150L, new BigDecimal("45000.00")),
            new QuarterlyPaymentReportResponse("2024Q1", "standard", 200L, new BigDecimal("30000.00"))
        );
        when(quarterlyPaymentReportService.generateQuarterlyPaymentReport()).thenReturn(mockData);

        // When & Then
        mockMvc.perform(get("/api/reports/quarterly-payments")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.error").doesNotExist())
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].quarter").value("2024Q1"))
                .andExpect(jsonPath("$.data[0].product_type").value("premium"))
                .andExpect(jsonPath("$.data[0].order_count").value(150))
                .andExpect(jsonPath("$.data[0].total_amount").value(45000.00))
                .andExpect(jsonPath("$.data[1].quarter").value("2024Q1"))
                .andExpect(jsonPath("$.data[1].product_type").value("standard"))
                .andExpect(jsonPath("$.data[1].order_count").value(200))
                .andExpect(jsonPath("$.data[1].total_amount").value(30000.00));
    }

    @Test
    void getQuarterlyPaymentReport_ShouldReturnEmptyData_WhenNoDataExists() throws Exception {
        // Given
        when(quarterlyPaymentReportService.generateQuarterlyPaymentReport()).thenReturn(Arrays.asList());

        // When & Then
        mockMvc.perform(get("/api/reports/quarterly-payments")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.error").doesNotExist())
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));
    }

    @Test
    void getQuarterlyPaymentReport_ShouldReturnErrorResponse_WhenServiceThrowsException() throws Exception {
        // Given
        when(quarterlyPaymentReportService.generateQuarterlyPaymentReport())
            .thenThrow(new RuntimeException("Database connection error"));

        // When & Then
        mockMvc.perform(get("/api/reports/quarterly-payments")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.data").doesNotExist())
                .andExpect(jsonPath("$.error").value("Failed to generate quarterly payment report: Database connection error"));
    }

    @Test
    void getQuarterlyPaymentReport_ShouldHaveCorrectEndpointMapping() throws Exception {
        // Given
        when(quarterlyPaymentReportService.generateQuarterlyPaymentReport()).thenReturn(Arrays.asList());

        // When & Then
        mockMvc.perform(get("/api/reports/quarterly-payments"))
                .andExpect(status().isOk());
    }

    @Test
    void getQuarterlyPaymentReport_ShouldProduceJsonContentType() throws Exception {
        // Given
        when(quarterlyPaymentReportService.generateQuarterlyPaymentReport()).thenReturn(Arrays.asList());

        // When & Then
        mockMvc.perform(get("/api/reports/quarterly-payments"))
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }
}
