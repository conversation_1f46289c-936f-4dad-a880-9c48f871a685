package com.kerryprops.kip.pmw.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration test for quarterly payment report functionality.
 * This test verifies the complete flow from controller to repository.
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
class QuarterlyPaymentReportIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    void getQuarterlyPaymentReport_ShouldReturnValidResponse_WhenEndpointIsCalled() throws Exception {
        // Note: This test assumes the read-only database is available and contains test data
        // In a real environment, you might want to use @Sql annotations to set up test data
        // or use TestContainers for database integration testing
        
        setUp();
        
        mockMvc.perform(get("/api/reports/quarterly-payments")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    void getQuarterlyPaymentReport_ShouldHaveCorrectResponseStructure() throws Exception {
        setUp();
        
        mockMvc.perform(get("/api/reports/quarterly-payments"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").exists())
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.error").doesNotExist());
    }
}
