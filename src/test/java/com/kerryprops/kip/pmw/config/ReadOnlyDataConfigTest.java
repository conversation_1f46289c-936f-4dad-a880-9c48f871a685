package com.kerryprops.kip.pmw.config;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * Test to verify that the read-only data configuration loads correctly.
 */
@SpringBootTest
@ActiveProfiles("test")
class ReadOnlyDataConfigTest {

    @Test
    void contextLoads() {
        // This test verifies that the Spring application context loads successfully
        // with the new read-only database configuration
    }
}
