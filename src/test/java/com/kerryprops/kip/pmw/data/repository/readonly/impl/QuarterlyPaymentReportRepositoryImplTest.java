package com.kerryprops.kip.pmw.data.repository.readonly.impl;

import com.kerryprops.kip.pmw.data.dto.QuarterlyPaymentReportDto;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Unit tests for QuarterlyPaymentReportRepositoryImpl.
 */
@ExtendWith(MockitoExtension.class)
class QuarterlyPaymentReportRepositoryImplTest {

    @Mock
    private EntityManager entityManager;

    @Mock
    private Query query;

    @InjectMocks
    private QuarterlyPaymentReportRepositoryImpl quarterlyPaymentReportRepository;

    private List<Object[]> mockQueryResults;

    @BeforeEach
    void setUp() {
        mockQueryResults = Arrays.asList(
            new Object[]{"2024Q1", "premium", 150L, new BigDecimal("45000.00")},
            new Object[]{"2024Q1", "standard", 200L, new BigDecimal("30000.00")},
            new Object[]{"2024Q2", "premium", 180L, new BigDecimal("54000.00")}
        );
    }

    @Test
    void getQuarterlyPaymentReport_ShouldReturnMappedResults_WhenQuerySucceeds() {
        // Given
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.getResultList()).thenReturn(mockQueryResults);

        // When
        List<QuarterlyPaymentReportDto> result = quarterlyPaymentReportRepository.getQuarterlyPaymentReport();

        // Then
        assertThat(result).hasSize(3);
        
        QuarterlyPaymentReportDto firstResult = result.get(0);
        assertThat(firstResult.getQuarter()).isEqualTo("2024Q1");
        assertThat(firstResult.getProductType()).isEqualTo("premium");
        assertThat(firstResult.getOrderCount()).isEqualTo(150L);
        assertThat(firstResult.getTotalAmount()).isEqualTo(new BigDecimal("45000.00"));
        
        QuarterlyPaymentReportDto secondResult = result.get(1);
        assertThat(secondResult.getQuarter()).isEqualTo("2024Q1");
        assertThat(secondResult.getProductType()).isEqualTo("standard");
        assertThat(secondResult.getOrderCount()).isEqualTo(200L);
        assertThat(secondResult.getTotalAmount()).isEqualTo(new BigDecimal("30000.00"));
        
        QuarterlyPaymentReportDto thirdResult = result.get(2);
        assertThat(thirdResult.getQuarter()).isEqualTo("2024Q2");
        assertThat(thirdResult.getProductType()).isEqualTo("premium");
        assertThat(thirdResult.getOrderCount()).isEqualTo(180L);
        assertThat(thirdResult.getTotalAmount()).isEqualTo(new BigDecimal("54000.00"));
    }

    @Test
    void getQuarterlyPaymentReport_ShouldReturnEmptyList_WhenNoResults() {
        // Given
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.getResultList()).thenReturn(Arrays.asList());

        // When
        List<QuarterlyPaymentReportDto> result = quarterlyPaymentReportRepository.getQuarterlyPaymentReport();

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void getQuarterlyPaymentReport_ShouldThrowRuntimeException_WhenQueryFails() {
        // Given
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.getResultList()).thenThrow(new RuntimeException("Database connection error"));

        // When & Then
        assertThatThrownBy(() -> quarterlyPaymentReportRepository.getQuarterlyPaymentReport())
            .isInstanceOf(RuntimeException.class)
            .hasMessage("Failed to execute quarterly payment report query");
    }

    @Test
    void getQuarterlyPaymentReport_ShouldHandleNullValues_WhenResultsContainNulls() {
        // Given
        List<Object[]> resultsWithNulls = Arrays.asList(
            new Object[]{"2024Q1", null, null, null},
            new Object[]{null, "premium", 0L, BigDecimal.ZERO}
        );
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.getResultList()).thenReturn(resultsWithNulls);

        // When
        List<QuarterlyPaymentReportDto> result = quarterlyPaymentReportRepository.getQuarterlyPaymentReport();

        // Then
        assertThat(result).hasSize(2);
        
        QuarterlyPaymentReportDto firstResult = result.get(0);
        assertThat(firstResult.getQuarter()).isEqualTo("2024Q1");
        assertThat(firstResult.getProductType()).isNull();
        assertThat(firstResult.getOrderCount()).isEqualTo(0L);
        assertThat(firstResult.getTotalAmount()).isEqualTo(BigDecimal.ZERO);
        
        QuarterlyPaymentReportDto secondResult = result.get(1);
        assertThat(secondResult.getQuarter()).isNull();
        assertThat(secondResult.getProductType()).isEqualTo("premium");
        assertThat(secondResult.getOrderCount()).isEqualTo(0L);
        assertThat(secondResult.getTotalAmount()).isEqualTo(BigDecimal.ZERO);
    }

    @Test
    void getQuarterlyPaymentReport_ShouldHandleDifferentNumberTypes_WhenResultsContainVariousNumbers() {
        // Given
        List<Object[]> resultsWithDifferentNumbers = Arrays.asList(
            new Object[]{"2024Q1", "premium", 150, 45000.00}, // int and double
            new Object[]{"2024Q1", "standard", 200L, new BigDecimal("30000.00")} // long and BigDecimal
        );
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.getResultList()).thenReturn(resultsWithDifferentNumbers);

        // When
        List<QuarterlyPaymentReportDto> result = quarterlyPaymentReportRepository.getQuarterlyPaymentReport();

        // Then
        assertThat(result).hasSize(2);
        
        QuarterlyPaymentReportDto firstResult = result.get(0);
        assertThat(firstResult.getOrderCount()).isEqualTo(150L);
        assertThat(firstResult.getTotalAmount()).isEqualTo(new BigDecimal("45000.0"));
        
        QuarterlyPaymentReportDto secondResult = result.get(1);
        assertThat(secondResult.getOrderCount()).isEqualTo(200L);
        assertThat(secondResult.getTotalAmount()).isEqualTo(new BigDecimal("30000.00"));
    }
}
