package com.kerryprops.kip.pmw.service.impl;

import com.kerryprops.kip.pmw.data.dto.QuarterlyPaymentReportDto;
import com.kerryprops.kip.pmw.data.repository.readonly.QuarterlyPaymentReportRepository;
import com.kerryprops.kip.pmw.webservice.resource.QuarterlyPaymentReportResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

/**
 * Unit tests for QuarterlyPaymentReportServiceImpl.
 */
@ExtendWith(MockitoExtension.class)
class QuarterlyPaymentReportServiceImplTest {

    @Mock
    private QuarterlyPaymentReportRepository quarterlyPaymentReportRepository;

    @InjectMocks
    private QuarterlyPaymentReportServiceImpl quarterlyPaymentReportService;

    private List<QuarterlyPaymentReportDto> mockReportData;

    @BeforeEach
    void setUp() {
        mockReportData = Arrays.asList(
            new QuarterlyPaymentReportDto("2024Q1", "premium", 150L, new BigDecimal("45000.00")),
            new QuarterlyPaymentReportDto("2024Q1", "standard", 200L, new BigDecimal("30000.00")),
            new QuarterlyPaymentReportDto("2024Q2", "premium", 180L, new BigDecimal("54000.00"))
        );
    }

    @Test
    void generateQuarterlyPaymentReport_ShouldReturnConvertedResponses_WhenDataExists() {
        // Given
        when(quarterlyPaymentReportRepository.getQuarterlyPaymentReport()).thenReturn(mockReportData);

        // When
        List<QuarterlyPaymentReportResponse> result = quarterlyPaymentReportService.generateQuarterlyPaymentReport();

        // Then
        assertThat(result).hasSize(3);
        
        QuarterlyPaymentReportResponse firstResponse = result.get(0);
        assertThat(firstResponse.getQuarter()).isEqualTo("2024Q1");
        assertThat(firstResponse.getProductType()).isEqualTo("premium");
        assertThat(firstResponse.getOrderCount()).isEqualTo(150L);
        assertThat(firstResponse.getTotalAmount()).isEqualTo(new BigDecimal("45000.00"));
        
        QuarterlyPaymentReportResponse secondResponse = result.get(1);
        assertThat(secondResponse.getQuarter()).isEqualTo("2024Q1");
        assertThat(secondResponse.getProductType()).isEqualTo("standard");
        assertThat(secondResponse.getOrderCount()).isEqualTo(200L);
        assertThat(secondResponse.getTotalAmount()).isEqualTo(new BigDecimal("30000.00"));
        
        QuarterlyPaymentReportResponse thirdResponse = result.get(2);
        assertThat(thirdResponse.getQuarter()).isEqualTo("2024Q2");
        assertThat(thirdResponse.getProductType()).isEqualTo("premium");
        assertThat(thirdResponse.getOrderCount()).isEqualTo(180L);
        assertThat(thirdResponse.getTotalAmount()).isEqualTo(new BigDecimal("54000.00"));
    }

    @Test
    void generateQuarterlyPaymentReport_ShouldReturnEmptyList_WhenNoDataExists() {
        // Given
        when(quarterlyPaymentReportRepository.getQuarterlyPaymentReport()).thenReturn(Arrays.asList());

        // When
        List<QuarterlyPaymentReportResponse> result = quarterlyPaymentReportService.generateQuarterlyPaymentReport();

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void generateQuarterlyPaymentReport_ShouldThrowRuntimeException_WhenRepositoryThrowsException() {
        // Given
        when(quarterlyPaymentReportRepository.getQuarterlyPaymentReport())
            .thenThrow(new RuntimeException("Database connection error"));

        // When & Then
        assertThatThrownBy(() -> quarterlyPaymentReportService.generateQuarterlyPaymentReport())
            .isInstanceOf(RuntimeException.class)
            .hasMessage("Failed to generate quarterly payment report");
    }

    @Test
    void generateQuarterlyPaymentReport_ShouldHandleNullValues_WhenDataContainsNulls() {
        // Given
        List<QuarterlyPaymentReportDto> dataWithNulls = Arrays.asList(
            new QuarterlyPaymentReportDto("2024Q1", null, null, null),
            new QuarterlyPaymentReportDto(null, "premium", 0L, BigDecimal.ZERO)
        );
        when(quarterlyPaymentReportRepository.getQuarterlyPaymentReport()).thenReturn(dataWithNulls);

        // When
        List<QuarterlyPaymentReportResponse> result = quarterlyPaymentReportService.generateQuarterlyPaymentReport();

        // Then
        assertThat(result).hasSize(2);
        
        QuarterlyPaymentReportResponse firstResponse = result.get(0);
        assertThat(firstResponse.getQuarter()).isEqualTo("2024Q1");
        assertThat(firstResponse.getProductType()).isNull();
        assertThat(firstResponse.getOrderCount()).isNull();
        assertThat(firstResponse.getTotalAmount()).isNull();
        
        QuarterlyPaymentReportResponse secondResponse = result.get(1);
        assertThat(secondResponse.getQuarter()).isNull();
        assertThat(secondResponse.getProductType()).isEqualTo("premium");
        assertThat(secondResponse.getOrderCount()).isEqualTo(0L);
        assertThat(secondResponse.getTotalAmount()).isEqualTo(BigDecimal.ZERO);
    }
}
