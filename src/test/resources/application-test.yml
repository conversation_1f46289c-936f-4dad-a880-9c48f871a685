wiremock:
  baseUrl: http://localhost:${wiremock.server.port}
spring:
  main:
    allow-bean-definition-overriding: true
  thymeleaf:
    cache: false
    prefix: 'classpath:/velocity/'
    encoding: UTF-8
    mode: HTML
    suffix: '.html'
  rabbitmq:
    password: MjQ0NTc0RDU2NDY2Q0M5NzU0OTM4QTA0OTI2RDlGNzFEMEU4QjdCRToxNzMwNDM5ODQyMzIz
    username: MjpyYWJiaXRtcS1zZXJ2ZXJsZXNzLWNuLXZjMjN6Yzc0azAyOkxUQUk1dEtBRTFQTWFmczkzeXNUQThnOQ==
    addresses: rabbitmq-serverless-cn-vc23zc74k02.cn-shanghai.amqp-0.vpc.mq.amqp.aliyuncs.com:5672
    virtual-host: kip-payment-middleware-service
    requested-heartbeat: 10
  application:
    name: payment-middleware-service
  jpa:
    show-sql: true
    generate-ddl: false
    properties:
      enable_lazy_load_no_trans: true
  datasource:
    url: ****************************************************************************************************************************************************************************************************************************
    username: dev_kip_payment_middleware
    password: "TTC5FGPI5ky^dIs9WolxpYV7m53Q$6R^"
    driver-class-name: com.mysql.cj.jdbc.Driver
  # Read-only replica datasource configuration for testing
  datasource-readonly:
    url: ******************************************************************************************************************************************************************************************************************************************
    username: dev_kip_payment_middleware
    password: "TTC5FGPI5ky^dIs9WolxpYV7m53Q$6R^"
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      idle-timeout: 600000
      maximum-pool-size: 10
      minimum-idle: 2
      connection-timeout: 30000
      connection-test-query: 'SELECT 1 + 1 FROM DUAL;'

# JDBC configuration properties for tests
jdbc:
  MysqlPaymentDB:
    GenerateDdl: false
    showSql: true
  quartz:
    autoStartup: false
    job-store-type: MEMORY
    properties:
      org:
        quartz:
          scheduler:
            instanceName: ${deployEnv}_PaymentScheduler
            instanceId: AUTO
            makeSchedulerThreadDaemon: true
            skipUpdateCheck: true
            batchTriggerAcquisitionMaxCount: 10
            batchTriggerAcquisitionFireAheadTimeWindow: 5000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            makeThreadsDaemons: true
  data:
    redis:
      host: r-uf6acipsdbwmuqbrle.redis.rds.aliyuncs.com
      password: EeuzaHThIv0oGd4u
jasypt:
  encryptor:
    password: ${ENCRYPT_KEY:kerryauthlocalpass}

cipher:
  jks:
    path: classpath:/store_client.jks
  keystore:
    password: password003
    private-key.alias-name: pmw_client
    private-key.password: password002
  v2:
    jks:
      path: classpath:/store_client.jks
    keystore:
      password: password003
      private-key.alias-name: pmw_client
      private-key.password: password002

pmw:
  sessionCacheHour: 6
  cipher:
    keystorePath: classpath:/store.jks
    keystorePassword: password003
    keyPassword: password002
    aliasName: pmw
    publicKeys:
      KERRY_PAY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAs+vEdvGn5Hexu393+wCxEBXf8Try5Do2JlVDPvJ6IzGoYDOyxIXLo+pqZoWorkLL9CwDqQr9+hO/2UCMuZypI6tFPIexip/QL+ZsqX2P+ZHWd8gibSoQg40imHFaYB/xA9LJGDl2mYU9W9iuMQcSD8fDAtCOjEAHiCFMMfv2GjgsAWrdHbEpTsXE3xymu4Ke1v3wCdxuE7xWIZ4WwRs4fWFvIvTwyjAHfVU3erWoEJsDhee3Q5/eLRIhi3+uD+iHh83mmR8/O2gx+C1FIuSHHIDEMHSmEbg3yaY9JG37REDfBf2CximXC6j8+jwSGqRGMiqmQi+i2nb4gC8tLrXhEwIDAQAB
      KIP_CAR_PARKING: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxiD1HIbLjNHoaxTlOBVFbUk6VPVbxmojHI0FGhYgiRXzKcVTDmrLy8a508ZE83PtiQJTWqNXW2a/qP3JplrPBW/NwwltiMhqOGNQIWBxnv3fHKAgSxbZT1Ex1zrLrFvfItRzU5Uz4bHLHRQGPC2msDXy0ZKrRpJdhIBwWbGqXxQ/ToI1kbQ7nqENb/DeIQqLS8/yuAvXe6gPIyAusHVrh7LLtOd3rICIYrEiXp5fkrV0dr7n9/4PZ28Wxznpzr8L7LZay7Ye4y+naINrXDhD6s6OYTY19WjSCC5u2qM/QpGlg8jZXdvM1010rljnmdxEoNigMi7bmXHEaBepkjinkwIDAQAB
  wxpay:
    host: ${wiremock.baseUrl}
    #    host: https://api.mch.weixin.qq.com
    lifeHost: https://payapp.wechatpay.cn
    parkingServiceStateChangeNotifyUrl: ${wiremock.baseUrl}/notify/parkingServiceStateChange
    spMerchantAccount: **********
    bizCircleMerchantAccount: **********
    spAppId: wxb81a622ed6d60adf
    merchantAccounts:
      **********:
        keystorePath: classpath:/apiclient_key_partner.pem
        merchantSerialNumber: "7886BB4A3148C2589086C08F7EAEA213FFAE9061"
        apiV3Key: "jsdkjkfkjsd89989llsdkkkshdnmwydk"
        apiV2Key: "jsdkjkfkjsd89989llsdkkkshdnmwyv2"
        name: "嘉里(中国)项目管理有限公司上海分公司"
    accountMappings:
      - companyCode: 41009 # 沈阳雅颂大苑
        projectId: SYYSDY
        Dev_Marketing: "**********"
        Car_Parking: "**********"
      - companyCode: 41009 # 沈阳雅颂居
        projectId: SYYSJ
        Dev_Marketing: **********-
        Car_Parking: **********+
      - companyCode: 31007 # BKC
        Dev_Marketing: **********@
        Car_Parking: **********#
      - companyCode: 31007 # 沈阳雅颂大苑
        Car_Parking: **********
        projectId: '188'
    wx-life:
      - companyCode: 42007 # 华庭二期
        merchantAccount: **********
        institution-id: ************
        city-id: 310000
        paymentItemIds:
          APP: "**************"
          OFFICIAL_ACCOUNT: "**************"
          MINI_PROGRAM: "**************"
  alipay:
    sign_type: RSA2
    alipayHost: https://openapi.alipay.com/gateway.do?charset=utf-8
    spMerchantAccount: '****************' #服务商开放平台第三方应用appId
    #supportedProjects: 192,
    #supportedProjects: 1222, 192, BKC, HKC, JAKC, KEC1, KEC3, PKC, SZKP, TKC, ZYJ, ZYJXQV2
    supportedProjects: 184, 192, BKC, HKC, JAKC, KEC1, KEC3, SKC, SYYSDY, SYYSJ, SZXP, TJYSJ, TKC
    withholdingProjects: 184, 192, HKC, JAKC, KEC1, KEC3, SKC, SYYSDY, SYYSJ, SZXP, TJYSJ, TKC
    merchantAccounts:
      ****************:
        keystorePath: classpath:/alipay_****************_key.pem
        merchantSerialNumber: "7886BB4A3148C2589086C08F7EAEA213FFAE9061"
        publicKeysPkcs8: "****************^MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA10qN8Xmpt+QTy4fkrTCi2/bOhKEyPYc5LV2dfwyDSLQrKDUZ1rKktQrJHMftMo+ZF77GWRsS67v7M9WB/M+CXbQcIpBPtfppnXswTZwlfqeMqh+kuVFNTp6zpFAWvdGrLf8dAaDZGe231fSiiQXJBTvVxtbglyqAID8RoIfwFgBQxTdIZ+VYomBm8hn//qDVI1DGZr+KyMec56aNvoP0j5FjhDfpLPObAXRLJfqWQXTRRR78PcLvO6udSx9G1J+0kD4JXo0Cmy2ULmRzUoo9rqwl4rgICd2yGjKgwIL2DiMdNlnfBL2ZNxLS6p+7llVlI4ySz8JcEcrEg3ggXufGywIDAQAB"
        name: "嘉里(中国)项目管理有限公司上海分公司"
      ****************:
        appAuthToken: '202207BB89591baa9d364bc4b2213565ce1cdE37' # app_auth_code
      ****************:
        appAuthToken: '202204BBf0d49d82e1234335b9a4763594c50X84'
    accountMappings:
      - companyCode: 41009 # 沈阳雅颂大苑
        Dev_Marketing: ****************
        Car_Parking: ****************
        projectId: 'SYYSDY'
      - companyCode: 41009 # 沈阳雅颂阁
        Dev_Marketing: ****************
        Car_Parking: ****************
        projectId: 'SYYSG'
  china-ums:
    appId: f0ec96ad2c3848b5b810e7aadf369e2f
    appKey: 775481e2556e4564985f5439a5e6a277
    vaMerchantNo: ***************
    vaTermNo: "********"
    msgSrcId: 3199
    newShopFlag: GWCXS
    orderPrefix: 3199
    salt: haS5kwGYphDykaKX6GyND7pa7RHmh54ttQ6caM5JEdPZtiaC
    url: ${wiremock.baseUrl}


    #以下全部为UMS在线签约配置
    contractingId: 2d9081bd919dbf560191b68a0b2f30cd
    contractingKey: jfu4szweksoxq5bz50bk28y6
    startContractingUrl:
      H5: https://yinshangpai.chinaums.com/self-sign-web/#/verify
      PC: https://yinshangpai.chinaums.com/self-sign-mobile/#/chooseRole

client:
  cipher:
    jks.path: classpath:/store_client.jks
    keystore:
      password: password003
      private-key.alias-name: pmw_client
      private-key.password: password002
    public_key_pkcs8:
      webcashier: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuRKdrvmS/1FdSvzljmq8jNEAPMLLxuTT+yCAw5gZ5VaXQ6BR1LudV2N6YOFYp7yUoOye3CobHI9GhAXrNaHrS3XoMk7/49g/uNNjJUTPzQ1xXZRbTnRgOkAbstYZDWY53S8X8fqk6ET0h3q2VwTuvkNkzZpiswkSFi7/SqLF0P+DEewyDTeywaGTUO4ls+8nTtl+T63LRSBGd8qUdOzgnfhr/YosKg3ePFyw1uC2UCK65KG47kmext+rLGFXT1o8oZ/Mlw5e+0aVSEoa6+MGvXmhdV0IURnw/DJlalisxppFDHjyg6amBqC08w7r1nooqZvnjCXiXR+LEPlfj6hm1wIDAQAB
      kip: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuRKdrvmS/1FdSvzljmq8jNEAPMLLxuTT+yCAw5gZ5VaXQ6BR1LudV2N6YOFYp7yUoOye3CobHI9GhAXrNaHrS3XoMk7/49g/uNNjJUTPzQ1xXZRbTnRgOkAbstYZDWY53S8X8fqk6ET0h3q2VwTuvkNkzZpiswkSFi7/SqLF0P+DEewyDTeywaGTUO4ls+8nTtl+T63LRSBGd8qUdOzgnfhr/YosKg3ePFyw1uC2UCK65KG47kmext+rLGFXT1o8oZ/Mlw5e+0aVSEoa6+MGvXmhdV0IURnw/DJlalisxppFDHjyg6amBqC08w7r1nooqZvnjCXiXR+LEPlfj6hm1wIDAQAB
  read:
    timeout: 2000
  connection:
    timeout: 2000
    ssl:
      validation:
        service: true
        payeco: true
        alipay: true
        wechatpay: true
        hostname:
          skipverify: true

#### wechatpay adapter cipher configuration
wechatpay:
  client.sign_type: RSA
  host: https://api.mch.weixin.qq.com
  kip_repairing:
    md5_key: 12345
    hmacSha256_key:
    cipher:
      p12:
        path: classpath:/wechatpay_cert.ticketing.p12
      keystore:
        password: 12345
  kip_order:
    md5_key: 12345
    hmacSha256_key:
    cipher:
      p12:
        path: classpath:/wechatpay_cert.ticketing.p12
      keystore:
        password: 12345
  crm:
    md5_key: 12345
    hmacSha256_key:
    cipher:
      p12:
        path: classpath:/wechatpay_cert.ticketing.p12
      keystore:
        password: 12345
  kip_billing:
    md5_key: 12345
    hmacSha256_key:
    cipher:
      p12:
        path: classpath:/wechatpay_cert.ticketing.p12
      keystore:
        password: 12345
  webcashier:
    host: https://latest.www:abcd:com/webcashier
  client:
    redirect_http_method: POST
#### alipayV2 adapter cipher configuration
alipay:
  v2:
    sign_type: RSA2
    host: https://openapi.alipay.com/gateway.do?charset=utf-8
    public_pkcs8_keys:

deployEnv: DEV

#### email configuration
email:
  endpoint: http://testapp.abcd.com/Webservice/FocussendWebservice.asmx?wsdl
  username: <EMAIL>
  password: abcd
  replyName: Digital PMW TECH SUPPORT
  replyEmail: <EMAIL>
  senderName: Payment Middleware - noreply
  senderEmail: <EMAIL>
  smtp:
    host: smtp.gmail.com
    port: 465
    protocol: smtps
    username: pmw.kerryprops.com
    password: PMW.kerryprops.com
    monitor_group: <EMAIL>
    biz_group: <EMAIL>

#### Tridion configuration
tridion:
  pmw:
    en_cn: classpath:/tridion.pmw.en_cn.properties
    zh_cn_cn: classpath:/tridion.pmw.zh_cn_cn.properties
    installment:
      en_cn: classpath:/tridion.pmw.installment.en_cn.properties
      zh_cn_cn: classpath:/tridion.pmw.installment.zh_cn_cn.properties

service:
  pmw:
    hostname: http://localhost:8080/services
    #vip: https://payment.kerryonvip.com/payment-middleware-service/services
    vip: https://dev-payment.kerryonvip.com/services
  unified-messaging-service: https://dev-kip-service-internal.kerryonvip.com/unified-messaging-service
  prod-live-flag-service: 'https://prod-live-flag-internal.kerryplus.com'
  #  billingCallback: 'https://dev-kip-service-internal.kerryonvip.com/kip-billing/s/apt/bill/direct_debits/agreement/%s/callback'
  billingCallback: '${wiremock.baseUrl}/notify/parkingServiceStateChange'
  points-service:
    base: 'https://dev-kip-service-internal.kerryonvip.com/points-service'
    membership-activated: '${service.points-service.base}/wechat_pay/memberPointsAuth'
    payment-notify: '${service.points-service.base}/wechat_pay/payment'
    refund-notify: '${service.points-service.base}/wechat_pay/refund'
    card-notify: '${service.points-service.base}/wechat_pay/member_card_open'
  billing-service:
    #    base: https://dev-kip-service-internal.kerryonvip.com/kip-billing
    base: http://localhost:8082
    debit-pre-verify: ${service.billing-service.base}/s/apt/direct_debits/psp/wechatpay/agreements/pre_verify
    confirm-agreement: ${service.billing-service.base}/s/apt/direct_debits/psp/wechatpay/agreements/confirm

management:
  endpoints:
    web:
      exposure:
        include: '*'
      base-path: /actuator
  endpoint:
    health:
      show-details: always

logging:
  config: classpath:log4j2-test.xml
