package com.kerryprops.kip.pmw.data.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Data Transfer Object for quarterly payment report results.
 * This DTO represents the aggregated payment data grouped by quarter and product type.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuarterlyPaymentReportDto {

    /**
     * Quarter name in format "YYYYQX" (e.g., "2024Q1")
     */
    private String quarter;

    /**
     * Product type from the order information
     */
    private String productType;

    /**
     * Count of distinct payment unit IDs (orders) for this quarter and product type
     */
    private Long orderCount;

    /**
     * Total amount for this quarter and product type
     */
    private BigDecimal totalAmount;

    /**
     * Constructor for JPA native query result mapping.
     * This constructor is used when mapping native query results to DTO objects.
     *
     * @param quarter Quarter name
     * @param productType Product type
     * @param orderCount Order count
     * @param totalAmount Total amount
     */
    public QuarterlyPaymentReportDto(String quarter, String productType, Number orderCount, Number totalAmount) {
        this.quarter = quarter;
        this.productType = productType;
        this.orderCount = orderCount != null ? orderCount.longValue() : 0L;
        this.totalAmount = totalAmount != null ? new BigDecimal(totalAmount.toString()) : BigDecimal.ZERO;
    }
}
