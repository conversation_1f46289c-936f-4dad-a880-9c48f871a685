package com.kerryprops.kip.pmw.data.repository.readonly.impl;

import com.kerryprops.kip.pmw.data.dto.QuarterlyPaymentReportDto;
import com.kerryprops.kip.pmw.data.repository.readonly.QuarterlyPaymentReportRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of QuarterlyPaymentReportRepository using JPA native queries.
 * This repository executes the quarterly payment report query on the read-only database replica.
 */
@Slf4j
@Repository
@Transactional(readOnly = true, transactionManager = "readOnlyTransactionManager")
public class QuarterlyPaymentReportRepositoryImpl implements QuarterlyPaymentReportRepository {

    @PersistenceContext(unitName = "readOnlyUnit")
    private EntityManager entityManager;

    /**
     * Native SQL query for quarterly payment report.
     * This query matches the requirements specified in the user request.
     */
    private static final String QUARTERLY_PAYMENT_REPORT_QUERY = """
        SELECT 
            quarter_name as quarter, 
            product_type, 
            COUNT(DISTINCT pay_unit_id) as order_count, 
            SUM(amount) as total_amount
        FROM (
            SELECT 
                CONCAT(YEAR(A.created_date), 'Q', QUARTER(A.created_date)) as quarter_name,
                D.product_type,
                A.pay_unit_id,
                B.amount
            FROM 
                db_prod_kip_payment_middleware.pmw_confirm_info A
            INNER JOIN 
                db_prod_kip_payment_middleware.pmw_pay_unit_info B 
                ON A.pay_unit_id = B.pay_unit_id
            INNER JOIN 
                db_prod_kip_payment_middleware.pmw_order_info D 
                ON B.order_no = D.order_no
            WHERE 
                A.created_date >= DATE_FORMAT(DATE_SUB(CONCAT(YEAR(NOW()), '-', (QUARTER(NOW()) - 1) * 3 + 1, '-01'), INTERVAL 3 MONTH), '%Y-%m-%d')
        ) AS derived_table
        GROUP BY 
            quarter_name, product_type
        ORDER BY 
            quarter_name, product_type
        """;

    @Override
    public List<QuarterlyPaymentReportDto> getQuarterlyPaymentReport() {
        log.info("Executing quarterly payment report query");
        
        try {
            Query query = entityManager.createNativeQuery(QUARTERLY_PAYMENT_REPORT_QUERY);
            
            @SuppressWarnings("unchecked")
            List<Object[]> results = query.getResultList();
            
            List<QuarterlyPaymentReportDto> reportData = results.stream()
                .map(this::mapToQuarterlyPaymentReportDto)
                .collect(Collectors.toList());
            
            log.info("Successfully retrieved {} quarterly payment report records", reportData.size());
            return reportData;
            
        } catch (Exception e) {
            log.error("Error executing quarterly payment report query", e);
            throw new RuntimeException("Failed to execute quarterly payment report query", e);
        }
    }

    /**
     * Maps a native query result row to QuarterlyPaymentReportDto.
     *
     * @param row The result row from native query
     * @return Mapped QuarterlyPaymentReportDto object
     */
    private QuarterlyPaymentReportDto mapToQuarterlyPaymentReportDto(Object[] row) {
        String quarter = (String) row[0];
        String productType = (String) row[1];
        Number orderCount = (Number) row[2];
        Number totalAmount = (Number) row[3];
        
        return new QuarterlyPaymentReportDto(quarter, productType, orderCount, totalAmount);
    }
}
