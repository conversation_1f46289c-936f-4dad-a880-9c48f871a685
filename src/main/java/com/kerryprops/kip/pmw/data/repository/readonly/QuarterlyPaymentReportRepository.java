package com.kerryprops.kip.pmw.data.repository.readonly;

import com.kerryprops.kip.pmw.data.dto.QuarterlyPaymentReportDto;

import java.util.List;

/**
 * Repository interface for quarterly payment report queries.
 * This repository is specifically designed for read-only operations on the replica database.
 */
public interface QuarterlyPaymentReportRepository {

    /**
     * Executes the quarterly payment report query to retrieve aggregated payment data
     * grouped by quarter and product type.
     * 
     * The query retrieves data from the last 3 months relative to the current quarter
     * and groups the results by quarter and product type, providing order count and total amount.
     *
     * @return List of quarterly payment report data
     */
    List<QuarterlyPaymentReportDto> getQuarterlyPaymentReport();
}
