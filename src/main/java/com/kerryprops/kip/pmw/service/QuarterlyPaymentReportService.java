package com.kerryprops.kip.pmw.service;

import com.kerryprops.kip.pmw.webservice.resource.QuarterlyPaymentReportResponse;

import java.util.List;

/**
 * Service interface for quarterly payment report operations.
 * This service provides business logic for generating quarterly payment reports
 * from the read-only database replica.
 */
public interface QuarterlyPaymentReportService {

    /**
     * Generates quarterly payment report data.
     * 
     * This method retrieves aggregated payment data grouped by quarter and product type
     * from the read-only database replica. The data includes order counts and total amounts
     * for each quarter and product type combination.
     *
     * @return List of quarterly payment report responses
     * @throws RuntimeException if there's an error retrieving the report data
     */
    List<QuarterlyPaymentReportResponse> generateQuarterlyPaymentReport();
}
