package com.kerryprops.kip.pmw.service.impl;

import com.kerryprops.kip.pmw.data.dto.QuarterlyPaymentReportDto;
import com.kerryprops.kip.pmw.data.repository.readonly.QuarterlyPaymentReportRepository;
import com.kerryprops.kip.pmw.service.QuarterlyPaymentReportService;
import com.kerryprops.kip.pmw.webservice.resource.QuarterlyPaymentReportResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of QuarterlyPaymentReportService.
 * This service handles the business logic for generating quarterly payment reports
 * and converting DTOs to response objects.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QuarterlyPaymentReportServiceImpl implements QuarterlyPaymentReportService {

    private final QuarterlyPaymentReportRepository quarterlyPaymentReportRepository;

    @Override
    public List<QuarterlyPaymentReportResponse> generateQuarterlyPaymentReport() {
        log.info("Starting quarterly payment report generation");
        
        try {
            // Retrieve data from repository
            List<QuarterlyPaymentReportDto> reportData = quarterlyPaymentReportRepository.getQuarterlyPaymentReport();
            
            // Convert DTOs to response objects
            List<QuarterlyPaymentReportResponse> responses = reportData.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
            
            log.info("Successfully generated quarterly payment report with {} records", responses.size());
            return responses;
            
        } catch (Exception e) {
            log.error("Error generating quarterly payment report", e);
            throw new RuntimeException("Failed to generate quarterly payment report", e);
        }
    }

    /**
     * Converts a QuarterlyPaymentReportDto to QuarterlyPaymentReportResponse.
     *
     * @param dto The DTO to convert
     * @return The converted response object
     */
    private QuarterlyPaymentReportResponse convertToResponse(QuarterlyPaymentReportDto dto) {
        return new QuarterlyPaymentReportResponse(
            dto.getQuarter(),
            dto.getProductType(),
            dto.getOrderCount(),
            dto.getTotalAmount()
        );
    }
}
