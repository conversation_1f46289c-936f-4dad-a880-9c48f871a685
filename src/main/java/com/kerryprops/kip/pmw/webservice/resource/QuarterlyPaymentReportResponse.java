package com.kerryprops.kip.pmw.webservice.resource;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Response model for quarterly payment report API.
 * This class represents a single row in the quarterly payment report response.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuarterlyPaymentReportResponse {

    /**
     * Quarter name in format "YYYYQX" (e.g., "2024Q1")
     */
    @JsonProperty("quarter")
    private String quarter;

    /**
     * Product type from the order information
     */
    @JsonProperty("product_type")
    private String productType;

    /**
     * Count of distinct orders for this quarter and product type
     */
    @JsonProperty("order_count")
    private Long orderCount;

    /**
     * Total amount for this quarter and product type
     */
    @JsonProperty("total_amount")
    private BigDecimal totalAmount;
}
