package com.kerryprops.kip.pmw.webservice;

import com.kerryprops.kip.pmw.service.QuarterlyPaymentReportService;
import com.kerryprops.kip.pmw.webservice.resource.ApiResponse;
import com.kerryprops.kip.pmw.webservice.resource.QuarterlyPaymentReportResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * REST Controller for quarterly payment report endpoints.
 * This controller provides API endpoints for generating and retrieving quarterly payment reports
 * from the read-only database replica.
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/reports", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Quarterly Payment Reports", description = "APIs for quarterly payment reporting")
public class QuarterlyPaymentReportController {

    private final QuarterlyPaymentReportService quarterlyPaymentReportService;

    /**
     * Generates and returns quarterly payment report data.
     * 
     * This endpoint executes a complex query on the read-only database replica to retrieve
     * aggregated payment data grouped by quarter and product type. The data includes
     * order counts and total amounts for each combination.
     *
     * @return ResponseEntity containing the quarterly payment report data
     */
    @GetMapping("/quarterly-payments")
    @Operation(
        summary = "Get quarterly payment report",
        description = "Retrieves quarterly payment report data grouped by quarter and product type. " +
                     "The report includes order counts and total amounts from the read-only database replica."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved quarterly payment report",
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON_VALUE,
                schema = @Schema(implementation = ApiResponse.class)
            )
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error occurred while generating the report",
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON_VALUE,
                schema = @Schema(implementation = ApiResponse.class)
            )
        )
    })
    public ResponseEntity<ApiResponse<List<QuarterlyPaymentReportResponse>>> getQuarterlyPaymentReport() {
        log.info("Received request for quarterly payment report");
        
        try {
            List<QuarterlyPaymentReportResponse> reportData = quarterlyPaymentReportService.generateQuarterlyPaymentReport();
            
            log.info("Successfully processed quarterly payment report request with {} records", reportData.size());
            return ResponseEntity.ok(ApiResponse.success(reportData));
            
        } catch (Exception e) {
            log.error("Error processing quarterly payment report request", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("Failed to generate quarterly payment report: " + e.getMessage()));
        }
    }
}
