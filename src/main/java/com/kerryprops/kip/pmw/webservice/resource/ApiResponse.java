package com.kerryprops.kip.pmw.webservice.resource;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Generic API response wrapper for consistent response format across all endpoints.
 * This follows the existing pattern in the payment middleware service.
 *
 * @param <T> The type of data being returned
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {

    /**
     * Indicates whether the API call was successful
     */
    @JsonProperty("success")
    private boolean success;

    /**
     * The actual data payload
     */
    @JsonProperty("data")
    private T data;

    /**
     * Error message if the call was not successful
     */
    @JsonProperty("error")
    private String error;

    /**
     * Creates a successful response with data
     *
     * @param data The data to return
     * @param <T> The type of data
     * @return ApiResponse with success=true and the provided data
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(true, data, null);
    }

    /**
     * Creates an error response with error message
     *
     * @param error The error message
     * @param <T> The type of data
     * @return ApiResponse with success=false and the provided error message
     */
    public static <T> ApiResponse<T> error(String error) {
        return new ApiResponse<>(false, null, error);
    }
}
