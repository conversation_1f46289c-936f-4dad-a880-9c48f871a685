# Quarterly Payment Report Feature

## Overview

This document describes the newly implemented quarterly payment report feature for the payment middleware service. This feature provides a REST API endpoint that executes quarterly payment reports with data retrieved from a read-only replica database.

## Features

- **Read-Only Database Connection**: Uses a separate read-only database replica to avoid impacting main database operations
- **Quarterly Aggregation**: Groups payment data by quarter and product type
- **REST API**: Provides a clean REST endpoint for retrieving report data
- **Comprehensive Testing**: Includes unit tests, integration tests, and error handling tests
- **Proper Error Handling**: Returns structured error responses for various failure scenarios
- **SQL Injection Protection**: Uses parameterized queries and proper input validation

## API Endpoint

### GET /api/reports/quarterly-payments

Retrieves quarterly payment report data grouped by quarter and product type.

#### Response Format

```json
{
  "success": true,
  "data": [
    {
      "quarter": "2024Q1",
      "product_type": "premium",
      "order_count": 150,
      "total_amount": 45000.00
    },
    {
      "quarter": "2024Q1", 
      "product_type": "standard",
      "order_count": 200,
      "total_amount": 30000.00
    }
  ]
}
```

#### Error Response Format

```json
{
  "success": false,
  "error": "Failed to generate quarterly payment report: Database connection error"
}
```

## Architecture

### Components

1. **QuarterlyPaymentReportController**: REST controller handling HTTP requests
2. **QuarterlyPaymentReportService**: Business logic layer
3. **QuarterlyPaymentReportRepository**: Data access layer with native SQL queries
4. **ReadOnlyDataConfig**: Configuration for read-only database connection
5. **DTOs and Response Models**: Data transfer objects for clean API responses

### Database Configuration

The feature uses a separate read-only database configuration:

- **Primary Database**: Used for normal application operations
- **Read-Only Replica**: Used exclusively for reporting queries to avoid performance impact

### SQL Query

The feature executes the following SQL query:

```sql
SELECT 
    quarter_name as quarter, 
    product_type, 
    COUNT(DISTINCT pay_unit_id) as order_count, 
    SUM(amount) as total_amount
FROM (
    SELECT 
        CONCAT(YEAR(A.created_date), 'Q', QUARTER(A.created_date)) as quarter_name,
        D.product_type,
        A.pay_unit_id,
        B.amount
    FROM 
        db_prod_kip_payment_middleware.pmw_confirm_info A
    INNER JOIN 
        db_prod_kip_payment_middleware.pmw_pay_unit_info B 
        ON A.pay_unit_id = B.pay_unit_id
    INNER JOIN 
        db_prod_kip_payment_middleware.pmw_order_info D 
        ON B.order_no = D.order_no
    WHERE 
        A.created_date >= DATE_FORMAT(DATE_SUB(CONCAT(YEAR(NOW()), '-', (QUARTER(NOW()) - 1) * 3 + 1, '-01'), INTERVAL 3 MONTH), '%Y-%m-%d')
) AS derived_table
GROUP BY 
    quarter_name, product_type
ORDER BY 
    quarter_name, product_type;
```

## Configuration

### Application Properties

Add the following configuration to your application properties:

```yaml
spring:
  # Read-only replica datasource configuration
  datasource-readonly:
    url: ****************************************************************************
    username: your_readonly_username
    password: your_readonly_password
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      idle-timeout: 600000
      maximum-pool-size: 50
      minimum-idle: 5
      connection-timeout: 30000
      connection-test-query: 'SELECT 1 + 1 FROM DUAL;'
```

## Testing

The feature includes comprehensive tests:

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test the complete flow from controller to repository
- **Error Handling Tests**: Verify proper error responses for various failure scenarios

### Running Tests

```bash
# Run all quarterly payment report tests
mvn test -Dtest="*QuarterlyPaymentReport*"

# Run specific test classes
mvn test -Dtest="QuarterlyPaymentReportServiceImplTest,QuarterlyPaymentReportRepositoryImplTest"
```

## Usage Examples

### Using curl

```bash
# Get quarterly payment report
curl -X GET "http://localhost:8080/api/reports/quarterly-payments" \
     -H "Content-Type: application/json"
```

### Using Java/Spring RestTemplate

```java
RestTemplate restTemplate = new RestTemplate();
String url = "http://localhost:8080/api/reports/quarterly-payments";
ApiResponse<List<QuarterlyPaymentReportResponse>> response = 
    restTemplate.getForObject(url, ApiResponse.class);
```

## Monitoring and Logging

The feature includes comprehensive logging for:

- Request processing
- Query execution
- Error scenarios
- Performance metrics

Log messages are structured and include correlation IDs for tracing.

## Security Considerations

- Uses read-only database connection to prevent data modification
- Implements proper input validation
- Uses parameterized queries to prevent SQL injection
- Follows existing authentication and authorization patterns

## Performance Considerations

- Uses read-only replica to avoid impacting main database performance
- Optimized connection pool settings for reporting workloads
- Efficient SQL query with proper indexing considerations
- Implements proper error handling to avoid resource leaks

## Future Enhancements

Potential future improvements:

1. **Caching**: Add Redis caching for frequently requested reports
2. **Pagination**: Add pagination support for large result sets
3. **Filtering**: Add date range and product type filtering parameters
4. **Export Formats**: Support CSV, Excel export formats
5. **Scheduled Reports**: Add scheduled report generation and email delivery
6. **Real-time Updates**: Add WebSocket support for real-time report updates
